globals
    // 全局变量：存储 UI 框架和触发器
    integer PanelFrame = 0              // 主背景框架（带边框的面板）
    integer TitleFrame = 0              // 大标题（“收藏面板”）
    integer DetailTitleFrame = 0        // 小标题（“详情”）
    integer DetailFrame = 0             // 详情文本区域
    integer ToggleButton = 0            // 开关按钮
    integer ToggleButtonBackdrop = 0    // 开关按钮背景
    // 注释：开关按钮高光现在由FDF的ControlMouseOverHighlight自动处理
    integer array CollectionSlots[16]   // 16 个收藏槽按钮
    integer array CollectionSlotBackdrops[16] // 16 个收藏槽图标背景
    // 注释：高光效果现在由FDF的ControlMouseOverHighlight自动处理
    integer DecorativeLine = 0          // 装饰分隔线
    trigger EscTrigger = null           // ESC 键触发器
    trigger ToggleTrigger = null        // 开关按钮触发器
    trigger array HoverTriggers[16]     // 每个收藏槽的悬停触发器
    boolean IsPanelVisible = false      // 面板是否可见

    // 纯JASS数据数组（替代GUI变量）
    string array CollectionDescriptions // 收藏品描述
    string array CollectionIcons       // 收藏品图标
    boolean CollectionDataInitialized = false // 数据初始化标记
endglobals

// 宽屏适配函数：手动计算坐标缩放（兼容老版本魔兽3）
function GetWidescreenScaleX takes nothing returns real
    // 基于4:3标准屏幕(0.8x0.6)，在宽屏上缩放X坐标
    // 4:3 = 1.33, 16:9 = 1.78, 21:9 = 2.33
    // 保守估计缩放因子为0.8，适应大多数宽屏显示器
    return 0.8
endfunction

function SetWidescreenPosition takes integer frame, real x, real y returns nothing
    local real scaledX = x * GetWidescreenScaleX()
    call DzFrameSetAbsolutePoint(frame, 4, scaledX, y)  // 4 = FRAMEPOINT_CENTER
endfunction

// 初始化收藏品数据
function InitCollectionData takes nothing returns nothing
    if CollectionDataInitialized then
        return
    endif

    // 设置默认描述
    set CollectionDescriptions[0] = "神秘的魔法卷轴，蕴含古老的力量"
    set CollectionDescriptions[1] = "传说中的战士之剑，锋利无比"
    set CollectionDescriptions[2] = "闪耀的法师宝石，增强魔法能力"
    set CollectionDescriptions[3] = "古老的护身符，提供神秘保护"
    set CollectionDescriptions[4] = "稀有的药剂，恢复生命力"
    set CollectionDescriptions[5] = "精灵的弓箭，射程极远"
    set CollectionDescriptions[6] = "龙鳞护甲，防御力惊人"
    set CollectionDescriptions[7] = "魔法戒指，增强法术威力"
    set CollectionDescriptions[8] = "圣光之盾，抵御邪恶"
    set CollectionDescriptions[9] = "暗影斗篷，隐匿身形"
    set CollectionDescriptions[10] = "雷电法杖，召唤闪电"
    set CollectionDescriptions[11] = "冰霜之心，冻结敌人"
    set CollectionDescriptions[12] = "火焰宝珠，燃烧一切"
    set CollectionDescriptions[13] = "治疗药水，快速恢复"
    set CollectionDescriptions[14] = "速度之靴，移动如风"
    set CollectionDescriptions[15] = "智慧之书，增长知识"

    // 设置默认图标
    set CollectionIcons[0] = "ReplaceableTextures\\CommandButtons\\BTNTomeOfRetraining.blp"
    set CollectionIcons[1] = "ReplaceableTextures\\CommandButtons\\BTNSteelMelee.blp"
    set CollectionIcons[2] = "ReplaceableTextures\\CommandButtons\\BTNOrbOfFire.blp"
    set CollectionIcons[3] = "ReplaceableTextures\\CommandButtons\\BTNPeriapt.blp"
    set CollectionIcons[4] = "ReplaceableTextures\\CommandButtons\\BTNHealingPotion.blp"
    set CollectionIcons[5] = "ReplaceableTextures\\CommandButtons\\BTNElvenArcher.blp"
    set CollectionIcons[6] = "ReplaceableTextures\\CommandButtons\\BTNHumanArmorUpOne.blp"
    set CollectionIcons[7] = "ReplaceableTextures\\CommandButtons\\BTNRingGreen.blp"
    set CollectionIcons[8] = "ReplaceableTextures\\CommandButtons\\BTNHolyBolt.blp"
    set CollectionIcons[9] = "ReplaceableTextures\\CommandButtons\\BTNShadowCloak.blp"
    set CollectionIcons[10] = "ReplaceableTextures\\CommandButtons\\BTNStaffOfSanctuary.blp"
    set CollectionIcons[11] = "ReplaceableTextures\\CommandButtons\\BTNOrbOfFrost.blp"
    set CollectionIcons[12] = "ReplaceableTextures\\CommandButtons\\BTNOrbOfFire.blp"
    set CollectionIcons[13] = "ReplaceableTextures\\CommandButtons\\BTNLesserRejuvPotion.blp"
    set CollectionIcons[14] = "ReplaceableTextures\\CommandButtons\\BTNBootsOfSpeed.blp"
    set CollectionIcons[15] = "ReplaceableTextures\\CommandButtons\\BTNSpellBookBLS.blp"

    set CollectionDataInitialized = true
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[调试] 收藏品数据初始化完成|r")
endfunction

// 更新详情文本：鼠标悬停时显示收藏品描述
function UpdateDetail takes integer slot returns nothing
    if CollectionDescriptions[slot] != null and CollectionDescriptions[slot] != "" then
        call DzFrameSetText(DetailFrame, "|cFFFFD700收藏品 " + I2S(slot) + "|r|n|n|cFF87CEEB" + CollectionDescriptions[slot] + "|r")
    else
        call DzFrameSetText(DetailFrame, "|cFFFFD700收藏品 " + I2S(slot) + "|r|n|n|cFF696969暂无描述信息|r")
    endif
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[调试] 显示详情: 收藏品 " + I2S(slot) + "|r")
endfunction

// 鼠标悬停事件：检测哪个槽被悬停并显示描述（高光由FDF自动处理）
function OnSlotHover takes nothing returns nothing
    local integer ydl_frame = DzGetTriggerUIEventFrame()
    local integer ydl_i = 0

    loop
        exitwhen ydl_i >= 16
        if CollectionSlots[ydl_i] == ydl_frame then
            // 更新详情文本（带颜色）
            if CollectionDescriptions[ydl_i] != null and CollectionDescriptions[ydl_i] != "" then
                call DzFrameSetText(DetailFrame, "|cFFFFD700收藏品 " + I2S(ydl_i) + "|r|n|n|cFF87CEEB" + CollectionDescriptions[ydl_i] + "|r")
            else
                call UpdateDetail(ydl_i)
            endif

            call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[调试] 显示详情: 收藏品 " + I2S(ydl_i) + "|r")
            return
        endif
        set ydl_i = ydl_i + 1
    endloop
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000[错误] 未找到匹配槽: " + I2S(ydl_frame) + "|r")
endfunction

// 鼠标离开事件：重置详情文本（高光由FDF自动处理）
function OnSlotLeave takes nothing returns nothing
    // 重置详情文本
    call DzFrameSetText(DetailFrame, "|cFF696969选择一个收藏品查看详情|r")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[调试] 详情已重置|r")
endfunction

// 开关面板：控制显示/隐藏
function TogglePanel takes nothing returns nothing
    local integer ydl_i = 0

    if IsPanelVisible then
        // 关闭面板：只保留大标题和小标题
        call DzFrameShow(PanelFrame, false)
        call DzFrameShow(DetailFrame, false)
        call DzFrameShow(ToggleButton, true) // 开关按钮始终可见
        call DzFrameShow(ToggleButtonBackdrop, true)
        loop
            exitwhen ydl_i >= 16
            call DzFrameShow(CollectionSlots[ydl_i], false)
            call DzFrameShow(CollectionSlotBackdrops[ydl_i], false)
            set ydl_i = ydl_i + 1
        endloop
        call DzFrameShow(TitleFrame, true)
        call DzFrameShow(DetailTitleFrame, true)
        call DzFrameShow(DecorativeLine, false)
        set IsPanelVisible = false
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFFF00[调试] 收藏面板已关闭，仅保留标题|r")
    else
        // 打开面板：显示所有内容
        call DzFrameShow(PanelFrame, true)
        call DzFrameShow(TitleFrame, true)
        call DzFrameShow(DetailTitleFrame, true)
        call DzFrameShow(DetailFrame, true)
        call DzFrameShow(ToggleButton, true)
        call DzFrameShow(ToggleButtonBackdrop, true)
        // ToggleButtonIcon已改为ToggleButtonBackdrop
        call DzFrameShow(DecorativeLine, true)
        set ydl_i = 0
        loop
            exitwhen ydl_i >= 16
            call DzFrameShow(CollectionSlots[ydl_i], true)
            call DzFrameShow(CollectionSlotBackdrops[ydl_i], true)
            set ydl_i = ydl_i + 1
        endloop
        set IsPanelVisible = true
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[调试] 收藏面板已显示|r")
    endif
endfunction

// 初始化收藏面板：创建所有 UI 元素并设置固定大小和位置
function InitCollectionPanel takes nothing returns nothing
    local integer ydl_i = 0
    local integer ydl_gameUI = DzGetGameUI()

    // 初始化收藏品数据
    call InitCollectionData()

    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[调试] 开始初始化收藏面板...|r")

    // 宽屏适配：手动计算坐标缩放（兼容老版本）
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[调试] 宽屏适配：使用手动坐标计算|r")

    // 加载 FDF 文件：定义 UI 样式
    call DzLoadToc("war3mapImported\CustomButton.toc") // 修改路径使用单反斜杠
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[调试] FDF 文件已加载: war3mapImported\CustomButton.toc|r")

    // 创建主背景框架（固定大小）
    set PanelFrame = DzCreateFrameByTagName("BACKDROP", "CollectionPanelBackdropInstance", ydl_gameUI, "CollectionPanelBackdrop", 0)
    if PanelFrame == 0 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000[错误] PanelFrame 创建失败，检查 FDF 和 TOC|r")
        return
    endif
    call DzFrameSetSize(PanelFrame, 0.4, 0.3) // 固定大小，宽度 0.4，高度 0.3（可调整）
    call SetWidescreenPosition(PanelFrame, 0.4, 0.32) // 使用宽屏适配定位
    call DzFrameShow(PanelFrame, false)
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "PanelFrame 创建: " + I2S(PanelFrame))

    // 创建大标题（固定大小和相对位置）
    set TitleFrame = DzCreateFrameByTagName("TEXT", "CollectionTitleInstance", PanelFrame, "CollectionTitle", 0)
    if TitleFrame == 0 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000[错误] TitleFrame 创建失败|r")
        return
    endif
    call DzFrameSetSize(TitleFrame, 0.2, 0.03) // 固定大小，宽度 0.2，高度 0.03（可调整）
    call DzFrameSetFont(TitleFrame, "Fonts\\MORPHEUS.ttf", 0.024, 0) // 使用魔法字体
    call DzFrameSetText(TitleFrame, "|cFFFFD700⚔️ 收藏面板 ⚔️|r") // 金色标题
    call DzFrameSetPoint(TitleFrame, 1, PanelFrame, 1, 0.0, -0.01) // 相对主框架顶部，Y 偏移 -0.01（可调整）
    call DzFrameShow(TitleFrame, false)
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "TitleFrame 创建: " + I2S(TitleFrame) + ", 文字: " + DzFrameGetText(TitleFrame))

    // 创建 16 个收藏槽（固定大小和相对位置）
    loop
        exitwhen ydl_i >= 16
        // 使用FDF中定义的CollectionSlotButton模板（包含自动高光效果）
        set CollectionSlots[ydl_i] = DzCreateFrameByTagName("BUTTON", "CollectionSlot" + I2S(ydl_i), PanelFrame, "CollectionSlotButton", 0)
        if CollectionSlots[ydl_i] == 0 then
            call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000[错误] CollectionSlot " + I2S(ydl_i) + " 创建失败|r")
            return
        endif

        // 查找FDF中定义的CollectionSlotIcon子框架
        set CollectionSlotBackdrops[ydl_i] = DzFrameFindByName("CollectionSlotIcon", CollectionSlots[ydl_i])
        if CollectionSlotBackdrops[ydl_i] == 0 then
            call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFFF00[警告] CollectionSlotIcon " + I2S(ydl_i) + " 未找到，使用按钮本身|r")
            set CollectionSlotBackdrops[ydl_i] = CollectionSlots[ydl_i]
        endif
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[调试] CollectionSlot " + I2S(ydl_i) + " 创建成功（含高光效果）|r")

        // 注释：高光效果现在由FDF的ControlMouseOverHighlight自动处理，无需手动创建

        call DzFrameSetSize(CollectionSlots[ydl_i], 0.05, 0.05) // 固定大小，可调整
        call DzFrameSetSize(CollectionSlotBackdrops[ydl_i], 0.05, 0.05)
        // 位置：相对主框架左上角，X: 0.01 + 横向间隔，Y: -0.07 + 纵向间隔（可调整）
        call DzFrameSetPoint(CollectionSlots[ydl_i], 0, PanelFrame, 0, 0.01 + (ydl_i - (ydl_i / 4) * 4) * 0.055, -0.07 - (ydl_i / 4) * 0.055)
        call DzFrameSetPoint(CollectionSlotBackdrops[ydl_i], 4, CollectionSlots[ydl_i], 4, 0.0, 0.0)

        if CollectionIcons[ydl_i] == null or CollectionIcons[ydl_i] == "" then
            call DzFrameSetTexture(CollectionSlotBackdrops[ydl_i], "ReplaceableTextures\\CommandButtons\\BTNTomeOfRetraining.blp", 0) // 使用重训之书图标
        else
            call DzFrameSetTexture(CollectionSlotBackdrops[ydl_i], CollectionIcons[ydl_i], 0)
        endif

        call DzFrameShow(CollectionSlots[ydl_i], false)
        call DzFrameShow(CollectionSlotBackdrops[ydl_i], false)

        set HoverTriggers[ydl_i] = CreateTrigger()
        call TriggerAddAction(HoverTriggers[ydl_i], function OnSlotHover)
        call DzFrameSetScriptByCode(CollectionSlots[ydl_i], 2, function OnSlotHover, false)
        call DzFrameSetScriptByCode(CollectionSlots[ydl_i], 3, function OnSlotLeave, false)
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[调试] CollectionSlot " + I2S(ydl_i) + " 创建: " + I2S(CollectionSlots[ydl_i]) + "|r")
        set ydl_i = ydl_i + 1
    endloop

    // 创建小标题（固定大小和相对位置）
    set DetailTitleFrame = DzCreateFrameByTagName("TEXT", "CollectionDetailTitleInstance", PanelFrame, "CollectionDetailTitle", 0)
    if DetailTitleFrame == 0 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000[错误] DetailTitleFrame 创建失败|r")
        return
    endif
    call DzFrameSetSize(DetailTitleFrame, 0.12, 0.03) // 固定大小，可调整
    call DzFrameSetFont(DetailTitleFrame, "Fonts\\FRIZQT__.ttf", 0.018, 0) // 字体大小可调整
    call DzFrameSetText(DetailTitleFrame, "|cFF87CEEB📖 详情|r") // 淡蓝色副标题
    call DzFrameSetPoint(DetailTitleFrame, 5, CollectionSlots[0], 5, 0.30, 0.0) // 右对齐第一列图标，X 偏移 0.02（可调整）
    call DzFrameShow(DetailTitleFrame, false)
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[调试] DetailTitleFrame 创建: " + I2S(DetailTitleFrame) + "|r")

    // 创建详情文本（固定大小和相对位置）
    set DetailFrame = DzCreateFrameByTagName("TEXT", "CollectionDetailTextInstance", PanelFrame, "CollectionDetailText", 0)
    if DetailFrame == 0 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000[错误] DetailFrame 创建失败|r")
        return
    endif
    call DzFrameSetSize(DetailFrame, 0.12, 0.17) // 固定大小，可调整
    call DzFrameSetFont(DetailFrame, "Fonts\\ARIALN.ttf", 0.013, 0) // 字体大小可调整
    call DzFrameSetText(DetailFrame, "|cFF696969选择一个收藏品查看详情|r") // 灰色默认文本
    call DzFrameSetPoint(DetailFrame, 2, CollectionSlots[0], 5, 0.30, -0.01) // 右对齐第一列图标，X 偏移 0.02，Y 偏移 -0.01（可调整）
    call DzFrameShow(DetailFrame, false)
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[调试] DetailFrame 创建: " + I2S(DetailFrame) + "|r")

    // 使用FDF中定义的ToggleButton模板（包含自动高光效果）
    set ToggleButton = DzCreateFrameByTagName("BUTTON", "ToggleButtonInstance", ydl_gameUI, "ToggleButton", 0)
    if ToggleButton == 0 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000[错误] ToggleButton 创建失败|r")
        return
    endif
    call DzFrameSetAbsolutePoint(ToggleButton, 3, 0.08, 0.50) // 固定位置，屏幕左上角
    call DzFrameShow(ToggleButton, true)

    // 查找FDF中定义的ToggleButtonIcon子框架
    set ToggleButtonBackdrop = DzFrameFindByName("ToggleButtonIcon", ToggleButton)
    if ToggleButtonBackdrop == 0 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFFF00[警告] ToggleButtonIcon 未找到，使用按钮本身|r")
        set ToggleButtonBackdrop = ToggleButton
    else
        // 设置开关按钮图标
        call DzFrameSetTexture(ToggleButtonBackdrop, "ReplaceableTextures\\CommandButtons\\BTNTomeOfRetraining.blp", 0)
    endif
    call DzFrameShow(ToggleButtonBackdrop, true)
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[调试] ToggleButton 创建成功（含高光效果）|r")
    
    // 设置触发器
    set ToggleTrigger = CreateTrigger()
    call TriggerAddAction(ToggleTrigger, function TogglePanel)
    call DzFrameSetScriptByCode(ToggleButton, 1, function TogglePanel, false)

    set EscTrigger = CreateTrigger()
    call DzTriggerRegisterKeyEvent(EscTrigger, 27, 0, true, null)
    call TriggerAddAction(EscTrigger, function TogglePanel)
    
    // 创建装饰分隔线
    set DecorativeLine = DzCreateFrameByTagName("BACKDROP", "DecorLine", PanelFrame, "", 0)
    call DzFrameSetTexture(DecorativeLine, "UI\\Widgets\\EscMenu\\Human\\human-separator.blp", 0)
    call DzFrameSetSize(DecorativeLine, 0.35, 0.003)
    call DzFrameSetPoint(DecorativeLine, 1, TitleFrame, 6, 0.0, -0.01)
    call DzFrameShow(DecorativeLine, false)

    set IsPanelVisible = false
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[调试] 收藏面板初始化完成，点击开关按钮或按 ESC 控制显示|r")
endfunction

// 主函数：地图启动时调用
function Main takes nothing returns nothing
    local trigger ydl_timer = CreateTrigger()
    call TriggerRegisterTimerEvent(ydl_timer, 0.10, false) // 延迟 0.10 秒初始化，可调整时间
    call TriggerAddAction(ydl_timer, function InitCollectionPanel)
    set ydl_timer = null
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[调试] Main 函数已调用|r")
    // 备注：此处不再监听窗口大小变化，因为 UI 已固定大小
endfunction